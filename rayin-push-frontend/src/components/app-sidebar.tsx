'use client'

import { useMemo, useCallback, memo } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  useSidebar,
} from '@/components/ui/sidebar'
import Image from 'next/image'
import { NavUser } from '@/components/layout/nav-user'

import { sidebarData } from '@/components/layout/data/sidebar-data'
import { navigationConfig, filterNavigationByRole } from '@/config/navigation'
import { useAuthStore } from '@/stores'
import { useTypedTranslation } from '@/hooks/useTypedTranslation'
import type { NavigationTranslations } from '@/types/i18n'

function AppSidebarComponent({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, isAuthenticated } = useAuthStore()
  const { t } = useTypedTranslation('navigation')
  const pathname = usePathname()
  const { state, isMobile, setOpenMobile } = useSidebar()

  // Use actual user data if available, otherwise use default
  const currentUser = user || sidebarData.user

  // 过滤导航项根据用户权限，使用 useMemo 缓存
  const filteredNavigation = useMemo(
    () => filterNavigationByRole(navigationConfig, user?.role, isAuthenticated),
    [user?.role, isAuthenticated]
  )

  // 处理导航项，保持分组结构
  const processedNavigation = useMemo(() => {
    return filteredNavigation.map((section) => ({
      ...section,
      items: section.items.map((item) => ({
        ...item,
        title: item.titleKey ? t(item.titleKey as keyof NavigationTranslations) : item.title,
      }))
    }))
  }, [filteredNavigation, t])

  // 获取当前语言代码
  const currentLocale = useMemo(() => {
    const localeMatch = pathname.match(/^\/([a-z]{2})/)
    return localeMatch ? localeMatch[1] : 'zh' // 默认为中文
  }, [pathname])

  // 检查路径是否匹配当前路由
  const isActiveRoute = useCallback((url: string) => {
    // 移除语言前缀来比较路径
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    const targetPath = url === '/' ? '/' : url
    return currentPath === targetPath || currentPath.startsWith(targetPath + '/')
  }, [pathname])

  // 为URL添加语言前缀
  const getLocalizedUrl = useCallback((url: string) => {
    return `/${currentLocale}${url}`
  }, [currentLocale])

  // 处理导航点击
  const handleNavClick = useCallback((e: React.MouseEvent, url: string) => {
    // 检查是否是当前页面
    const currentPath = pathname.replace(/^\/[a-z]{2}/, '') || '/'
    const targetPath = url === '/' ? '/' : url

    // 如果是当前页面，不需要加载
    if (currentPath === targetPath) {
      e.preventDefault()
      return
    }

    // 在移动端点击导航项后关闭侧边栏
    if (isMobile) {
      setOpenMobile(false)
    }
    // 阻止事件冒泡，防止触发侧边栏切换
    e.stopPropagation()
  }, [pathname, isMobile, setOpenMobile])

  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Image
                  src="/logo.svg"
                  alt="Rayin Push Logo"
                  width={20}
                  height={20}
                />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">Rayin Push</span>
                <span className="truncate text-xs text-muted-foreground">消息推送系统</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {processedNavigation.map((section) => (
          <SidebarGroup key={section.id}>
            {section.title && state === 'expanded' && (
              <SidebarGroupLabel className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
                {section.titleKey ? t(section.titleKey as keyof NavigationTranslations) : section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu>
                {section.items.map((item, index) => (
                  <SidebarMenuItem key={`${item.href}-${index}`}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActiveRoute(item.href)}
                      tooltip={state === 'collapsed' ? item.title : undefined}
                    >
                      <Link
                        href={getLocalizedUrl(item.href)}
                        onClick={(e) => handleNavClick(e, item.href)}
                        className="flex items-center gap-3"
                      >
                        {item.icon && (
                          <div className="flex size-4 items-center justify-center">
                            <item.icon className="size-4" />
                          </div>
                        )}
                        <span className="flex-1">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={currentUser} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export const AppSidebar = memo(AppSidebarComponent)